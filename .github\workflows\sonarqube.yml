name: SonarQube Analysis

on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop

env:
  SONAR_PROJECT_KEY: openautomate-frontend
  SONAR_PROJECT_NAME: openautomate-frontend
  SONAR_HOST_URL: http://sonar.openautomate.me
  SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

jobs:
  sonar:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          fetch-depth: 0 # Fetch all history for accurate blame information

      # Step 2: Set up Node.js
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      # Step 3: Install dependencies
      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      # Step 4: Run linter
      - name: Run ESLint
        run: npm run lint

      # Step 5: Install SonarQube Scanner
      - name: Install SonarQube Scanner
        uses: SonarSource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}
        with:
          args: >
            -Dsonar.projectKey=${{ env.SONAR_PROJECT_KEY }}
            -Dsonar.projectName=${{ env.SONAR_PROJECT_NAME }}
            -Dsonar.sources=src
            -Dsonar.exclusions=**/*.test.ts,**/*.test.tsx,node_modules/**/*

      # Step 6: SonarQube Quality Gate check
      - name: SonarQube Quality Gate check
        uses: sonarsource/sonarqube-quality-gate-action@master
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}

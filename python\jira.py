import requests
import json
from requests.auth import HTTPBasicAuth

# Configuration
JIRA_BASE_URL = "https://openautomate.atlassian.net"
EMAIL = "<EMAIL>"  # Replace with your Jira email
API_TOKEN = "ATATT3xFfGF0OruYTghANP-iVlUlTp-rvva-mpl1koMA3q7u6eWyz0m0S8crkmRMjg4eLjQZKmHI05fM64Lb1iX-hTYNfrgPwt0L1CoKnh94_ximZpORFc5SZ7Bffp0-G_Qlw3u7YL9God9JBNpJGKg7kR1JRMDNru_4GXiP3rxeqzAkI4nXPwQ=AB1234C2"  # Replace with your API token
PROJECT_KEY = "OA"

def get_all_jira_tickets():
    """
    Retrieve all tickets from the OpenAutomate project
    """
    all_issues = []
    start_at = 0
    max_results = 100  # <PERSON><PERSON>'s max per request
    
    while True:
        # JQL query to get all tickets in the project
        jql = f"project = {PROJECT_KEY} ORDER BY created DESC"
        
        # API endpoint
        url = f"{JIRA_BASE_URL}/rest/api/3/search"
        
        # Parameters
        params = {
            "jql": jql,
            "startAt": start_at,
            "maxResults": max_results,
            "fields": [
                "key", "summary", "status", "issuetype", "assignee", 
                "created", "updated", "priority", "reporter", "description",
                "labels", "components", "fixVersions", "resolution"
            ]
        }
        
        # Make the request
        response = requests.get(
            url,
            params=params,
            auth=HTTPBasicAuth(EMAIL, API_TOKEN),
            headers={"Accept": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"Error: {response.status_code}")
            print(response.text)
            break
            
        data = response.json()
        issues = data.get("issues", [])
        
        if not issues:
            break
            
        all_issues.extend(issues)
        
        # Check if we've got all issues
        if len(issues) < max_results:
            break
            
        start_at += max_results
        print(f"Retrieved {len(all_issues)} tickets so far...")
    
    return {
        "total": len(all_issues),
        "issues": all_issues
    }

def save_to_file(data, filename="jira_tickets.json"):
    """Save the data to a JSON file"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"Data saved to {filename}")

def main():
    print("Fetching all Jira tickets from OpenAutomate project...")
    
    # Get all tickets
    tickets_data = get_all_jira_tickets()
    
    print(f"Total tickets retrieved: {tickets_data['total']}")
    
    # Save to file
    save_to_file(tickets_data)
    
    # Print summary
    print("\nTicket Summary:")
    issue_types = {}
    statuses = {}
    
    for issue in tickets_data['issues']:
        # Count by issue type
        issue_type = issue['fields']['issuetype']['name']
        issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
        
        # Count by status
        status = issue['fields']['status']['name']
        statuses[status] = statuses.get(status, 0) + 1
    
    print("\nBy Issue Type:")
    for issue_type, count in sorted(issue_types.items()):
        print(f"  {issue_type}: {count}")
    
    print("\nBy Status:")
    for status, count in sorted(statuses.items()):
        print(f"  {status}: {count}")

if __name__ == "__main__":
    main()
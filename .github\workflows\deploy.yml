name: Deploy OpenAutomate Frontend

on:
  push:
    branches: [develop]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy to server
        uses: appleboy/scp-action@v0.1.4
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_KEY }}
          source: 'src/**, public/**, package.json, package-lock.json, next.config.ts, tsconfig.json, postcss.config.mjs, .env.production, components.json'
          target: '/var/www/openautomate/frontend'

      - name: Build and restart frontend
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /var/www/openautomate/frontend
            npm ci
            rm -f .env
            NODE_ENV=production npm run build
            pm2 restart openautomate-frontend --update-env || NODE_ENV=production pm2 start npm --name openautomate-frontend -- start

import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/providers/theme-provider'
import { AuthProvider } from '@/providers/auth-provider'
import { config } from '@/lib/config'
import { Toaster } from '@/components/ui/toaster'
import { ToastProvider } from '@/components/ui/toast-provider'
import { LocaleProvider } from '@/providers/locale-provider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: config.app.name,
  description: 'Automate your business processes with OpenAutomate',
  authors: [
    {
      name: 'OpenAutomate Team',
    },
  ],
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <LocaleProvider>
          <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
            <ToastProvider>
              <AuthProvider>
                <div className="min-h-screen flex flex-col antialiased bg-background">
                  {children}
                </div>
                <Toaster />
              </AuthProvider>
            </ToastProvider>
          </ThemeProvider>
        </LocaleProvider>
      </body>
    </html>
  )
}
